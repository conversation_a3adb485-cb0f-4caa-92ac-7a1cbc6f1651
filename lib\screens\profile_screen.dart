import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/auth_service.dart';
import '../services/auth_state_manager.dart';
import '../utils/navigation_helper.dart';
import '../config/supabase_config.dart';
import '../generated/l10n/app_localizations.dart';
import '../widgets/language_picker.dart';
import '../services/review_service.dart';
import '../screens/help_support_screen.dart';
import 'version_info_screen.dart';
import 'terms_of_service_screen.dart';
import 'privacy_policy_screen.dart';
import 'paywall_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isLoading = false;
  bool _isEditMode = false;
  bool _isUploadingImage = false;

  // Controllers for edit mode
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  // Image picker and profile picture
  final ImagePicker _imagePicker = ImagePicker();
  String? _profileImageUrl;
  String? _originalProfileImageUrl; // Store original URL to restore on cancel

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAnimations();
    _loadUserData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(
        CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut));

    _fadeController.forward();
  }

  void _loadUserData() {
    final user = AuthService.currentUser;
    if (user != null && !user.isAnonymous) {
      // Load username from user metadata
      _usernameController.text = user.userMetadata?['username'] ?? '';

      // Load email from user auth data (read-only)
      _emailController.text = user.email ?? '';

      // Load profile picture URL from user metadata
      _profileImageUrl = user.userMetadata?['profile_picture_url'];
      _originalProfileImageUrl =
          _profileImageUrl; // Store original for cancel functionality
    } else {
      // Clear fields for guest users
      _usernameController.clear();
      _emailController.clear();
      _profileImageUrl = null;
      _originalProfileImageUrl = null;
    }
  }

  Future<void> _refreshUserData() async {
    try {
      // Refresh user data from Supabase
      final supabase = SupabaseConfig.client;
      await supabase.auth.refreshSession();

      if (mounted) {
        setState(() {
          _loadUserData();
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to refresh user data: $e');
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _fadeController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // Refresh user data when app comes back to foreground
      _refreshUserData();
    }
  }

  void _toggleEditMode() {
    setState(() {
      _isEditMode = !_isEditMode;
      if (!_isEditMode) {
        // Reset controllers and profile image when canceling edit
        final user = AuthService.currentUser;
        if (user != null && !user.isAnonymous) {
          _usernameController.text = user.userMetadata?['username'] ?? '';
          _emailController.text = user.email ?? '';
          // Restore original profile image URL (don't lose uploaded but unsaved images)
          _profileImageUrl = _originalProfileImageUrl;
        } else {
          _usernameController.clear();
          _emailController.clear();
          _profileImageUrl = null;
        }
      }
    });
  }

  Future<void> _saveProfile() async {
    final user = AuthService.currentUser;
    if (user == null || user.isAnonymous) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).profileEditingNotAvailableForGuests,
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    // Validate username input
    final newUsername = _usernameController.text.trim();

    if (newUsername.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).usernameCannotBeEmpty,
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    if (newUsername.length < 2 || newUsername.length > 30) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)
                  .usernameMustBeBetween2And30Characters,
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final supabase = SupabaseConfig.client;

      // Update user metadata (username and profile picture URL)
      final updateData = <String, dynamic>{
        'username': newUsername,
      };

      // Include profile picture URL if it exists
      if (_profileImageUrl != null) {
        updateData['profile_picture_url'] = _profileImageUrl;
      }

      await supabase.auth.updateUser(
        UserAttributes(
          data: updateData,
        ),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).profileUpdatedSuccessfully,
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.green,
          ),
        );

        setState(() {
          _isEditMode = false;
          // Update original profile image URL after successful save
          _originalProfileImageUrl = _profileImageUrl;
        });
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)
                  .failedToUpdateProfile(error.toString()),
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _changeProfilePicture() async {
    final user = AuthService.currentUser;
    if (user == null || user.isAnonymous) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)
                  .profilePictureEditingNotAvailableForGuests,
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    // Show image source selection dialog
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFF7F9FC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Select Image Source',
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Color(0xFF0D76FF)),
                title: Text(
                  'Camera',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                onTap: () => Navigator.of(context).pop(ImageSource.camera),
              ),
              ListTile(
                leading:
                    const Icon(Icons.photo_library, color: Color(0xFF0D76FF)),
                title: Text(
                  'Gallery',
                  style: GoogleFonts.instrumentSans(
                    fontSize: 16,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                onTap: () => Navigator.of(context).pop(ImageSource.gallery),
              ),
            ],
          ),
        );
      },
    );

    if (source == null) return;

    try {
      setState(() {
        _isUploadingImage = true;
      });

      // Pick image
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (pickedFile == null) {
        setState(() {
          _isUploadingImage = false;
        });
        return;
      }

      // Upload to Supabase Storage
      final supabase = SupabaseConfig.client;
      final fileName =
          'profile_${user.id}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = 'profiles/$fileName';

      // Delete old profile picture if it exists
      if (_originalProfileImageUrl != null) {
        try {
          // Extract file path from URL
          final uri = Uri.parse(_originalProfileImageUrl!);
          final oldFilePath = uri.pathSegments
              .skip(5)
              .join('/'); // Skip /storage/v1/object/public/avatars/
          await supabase.storage.from('avatars').remove([oldFilePath]);
        } catch (e) {
          // Continue even if deletion fails - don't block new upload
          if (kDebugMode) {
            print('Failed to delete old profile picture: $e');
          }
        }
      }

      await supabase.storage
          .from('avatars')
          .uploadBinary(filePath, await pickedFile.readAsBytes());

      // Get public URL
      final imageUrl = supabase.storage.from('avatars').getPublicUrl(filePath);

      // Immediately save the profile picture to user metadata for persistence
      await supabase.auth.updateUser(
        UserAttributes(
          data: {
            'profile_picture_url': imageUrl,
            // Preserve existing username if it exists
            if (user.userMetadata?['username'] != null)
              'username': user.userMetadata!['username'],
          },
        ),
      );

      setState(() {
        _profileImageUrl = imageUrl;
        _originalProfileImageUrl = imageUrl; // Update original URL
        _isUploadingImage = false;
      });

      // Refresh user data to ensure consistency
      await _refreshUserData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).profilePictureUpdatedSuccessfully,
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (error) {
      setState(() {
        _isUploadingImage = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context)
                  .failedToUploadImage(error.toString()),
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleLogout() async {
    try {
      final isGuest = AuthService.isGuestUser;

      // Sign out the user
      await AuthStateManager().signOut();

      // Handle navigation based on user type
      if (mounted) {
        NavigationHelper.handleLogoutNavigation(context, isGuest: isGuest);
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Logout failed: ${error.toString()}',
              style: GoogleFonts.instrumentSans(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Opens the feature request website in external browser
  Future<void> _openFeatureRequestWebsite() async {
    const String featureRequestUrl =
        'https://studio--tripwisego-request.us-central1.hosted.app';

    try {
      final uri = Uri.parse(featureRequestUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );

        if (kDebugMode) {
          print(
              'Successfully opened feature request website: $featureRequestUrl');
        }
      } else {
        throw Exception('Could not launch $featureRequestUrl');
      }
    } catch (error) {
      if (kDebugMode) {
        print('Error opening feature request website: $error');
      }

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Unable to open feature request website. Please check your internet connection and try again.',
              style: GoogleFonts.instrumentSans(),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showLogoutDialog() {
    final isGuest = AuthService.isGuestUser;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFFF7F9FC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            isGuest ? 'End Guest Session' : 'Logout',
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF2D3748),
            ),
          ),
          content: Text(
            isGuest
                ? 'Are you sure you want to end your guest session? You\'ll return to the login screen.'
                : 'Are you sure you want to logout?',
            style: GoogleFonts.instrumentSans(
              fontSize: 16,
              color: const Color(0xFF718096),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF718096),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _handleLogout();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0D76FF),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                isGuest ? 'End Session' : 'Logout',
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  const SizedBox(height: 20), // Top padding instead of header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "Profile",
                        style: GoogleFonts.instrumentSans(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2D3748),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20), // Top padding instead of header
                  _buildProfileCard(),
                  const SizedBox(height: 20),
                  _buildMenuItems(),
                  const SizedBox(height: 20),
                  _buildSupportSection(),
                  const SizedBox(
                      height: 100), // Extra space for bottom navigation
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileCard() {
    final user = AuthService.currentUser;
    final isGuest = user?.isAnonymous ?? false;

    // Get real user data
    final username = isGuest
        ? AppLocalizations.of(context).guestUser
        : (user?.userMetadata?['username']?.isNotEmpty == true
            ? user!.userMetadata!['username']
            : user?.email?.split('@')[0] ?? 'User');

    final email = isGuest ? '<EMAIL>' : (user?.email ?? '');
    final subtitle = isGuest
        ? AppLocalizations.of(context).exploringAsGuest
        : AppLocalizations.of(context).readyForAdventure;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Avatar
          Stack(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFF0D76FF),
                  borderRadius: BorderRadius.circular(40),
                  image: _profileImageUrl != null
                      ? DecorationImage(
                          image: NetworkImage(_profileImageUrl!),
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: _profileImageUrl == null
                    ? Icon(
                        isGuest ? Icons.person_outline : Icons.person,
                        size: 40,
                        color: Colors.white,
                      )
                    : null,
              ),

              // Edit button for profile picture (only for non-guest users)
              if (!isGuest) ...[
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: _changeProfilePicture,
                    child: Container(
                      width: 28,
                      height: 28,
                      decoration: BoxDecoration(
                        color: const Color(0xFF0D76FF),
                        borderRadius: BorderRadius.circular(14),
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: _isUploadingImage
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(
                              Icons.camera_alt,
                              size: 16,
                              color: Colors.white,
                            ),
                    ),
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 16),

          // Username/Display Name
          if (_isEditMode && !isGuest) ...[
            TextField(
              controller: _usernameController,
              style: GoogleFonts.instrumentSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3748),
              ),
              textAlign: TextAlign.center,
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context).username,
                hintStyle: GoogleFonts.instrumentSans(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF718096),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF0D76FF)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide:
                      const BorderSide(color: Color(0xFF0D76FF), width: 2),
                ),
              ),
            ),
          ] else ...[
            Text(
              username,
              style: GoogleFonts.instrumentSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3748),
              ),
            ),
          ],

          const SizedBox(height: 8),

          // Email display (read-only)
          if (!isGuest && email.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F9FC),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE2E8F0)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.email_outlined,
                    size: 16,
                    color: Color(0xFF718096),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    email,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      color: const Color(0xFF718096),
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.lock_outline,
                    size: 14,
                    color: Color(0xFF718096),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Email cannot be changed',
              style: GoogleFonts.instrumentSans(
                fontSize: 12,
                color: const Color(0xFF718096),
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 8),
          ],

          Text(
            subtitle,
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
            ),
          ),

          const SizedBox(height: 20),

          // Edit Profile Buttons
          if (!isGuest) ...[
            if (_isEditMode) ...[
              // Save and Cancel buttons in edit mode
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : _toggleEditMode,
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(0xFF718096),
                        side: const BorderSide(color: Color(0xFF718096)),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        AppLocalizations.of(context).cancel,
                        style: GoogleFonts.instrumentSans(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveProfile,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0D76FF),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              AppLocalizations.of(context).saveChanges,
                              style: GoogleFonts.instrumentSans(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              // Edit Profile button in view mode
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _toggleEditMode,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0D76FF),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    AppLocalizations.of(context).editProfile,
                    style: GoogleFonts.instrumentSans(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ] else ...[
            // Guest user message
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Username and avatar editing is not available for guest users. Sign up to customize your profile!',
                textAlign: TextAlign.center,
                style: GoogleFonts.instrumentSans(
                  fontSize: 14,
                  color: const Color(0xFF0D76FF),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMenuItems() {
    return Column(
      children: [
        _buildMenuItem(
          icon: Icons.list_alt_outlined,
          title: AppLocalizations.of(context).plan,
          onTap: () {
            Navigator.of(context).push(
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    const PaywallScreen(),
                transitionDuration: const Duration(milliseconds: 300),
                reverseTransitionDuration: const Duration(milliseconds: 300),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  const begin = Offset(1.0, 0.0);
                  const end = Offset.zero;
                  const curve = Curves.easeInOut;

                  var tween = Tween(begin: begin, end: end).chain(
                    CurveTween(curve: curve),
                  );

                  return SlideTransition(
                    position: animation.drive(tween),
                    child: child,
                  );
                },
              ),
            );
          },
        ),
        const SizedBox(height: 12),
        _buildMenuItem(
          icon: Icons.language_outlined,
          title: AppLocalizations.of(context).language,
          onTap: () {
            showLanguagePickerBottomSheet(
              context,
              onLanguageChanged: (languageCode) {
                // Language change is handled by the LocalizationService
                // The UI will automatically update due to ListenableBuilder in main.dart
              },
            );
          },
        ),
        const SizedBox(height: 12),
        _buildLegalMenuItem(),
        const SizedBox(height: 12),
        _buildMenuItem(
          icon: Icons.info_outline,
          title: AppLocalizations.of(context).versionInfo,
          onTap: () {
            Navigator.of(context).push(
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    const VersionInfoScreen(),
                transitionDuration: const Duration(milliseconds: 300),
                reverseTransitionDuration: const Duration(milliseconds: 300),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  const begin = Offset(1.0, 0.0);
                  const end = Offset.zero;
                  const curve = Curves.easeInOut;

                  var tween = Tween(begin: begin, end: end).chain(
                    CurveTween(curve: curve),
                  );

                  return SlideTransition(
                    position: animation.drive(tween),
                    child: child,
                  );
                },
              ),
            );
          },
        ),
        const SizedBox(height: 12),
        _buildMenuItem(
          icon: Icons.logout,
          title: AppLocalizations.of(context).signOut,
          onTap: _showLogoutDialog,
          isDestructive: true,
        ),
      ],
    );
  }

  Widget _buildLegalMenuItem() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          ListTile(
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const TermsOfServiceScreen(),
                ),
              );
            },
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.description_outlined,
                color: Color(0xFF0D76FF),
                size: 20,
              ),
            ),
            title: Text(
              AppLocalizations.of(context).termsOfService,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2D3748),
              ),
            ),
            trailing: const Icon(
              Icons.chevron_right,
              color: Color(0xFF718096),
              size: 20,
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            height: 1,
            color: const Color(0xFFF7F9FC),
          ),
          ListTile(
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PrivacyPolicyScreen(),
                ),
              );
            },
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF0D76FF).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.privacy_tip_outlined,
                color: Color(0xFF0D76FF),
                size: 20,
              ),
            ),
            title: Text(
              AppLocalizations.of(context).privacyPolicy,
              style: GoogleFonts.instrumentSans(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2D3748),
              ),
            ),
            trailing: const Icon(
              Icons.chevron_right,
              color: Color(0xFF718096),
              size: 20,
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        onTap: onTap,
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isDestructive
                ? Colors.red.withOpacity(0.1)
                : const Color(0xFF0D76FF).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: isDestructive ? Colors.red : const Color(0xFF0D76FF),
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: GoogleFonts.instrumentSans(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: isDestructive ? Colors.red : const Color(0xFF2D3748),
          ),
        ),
        trailing: const Icon(
          Icons.chevron_right,
          color: Color(0xFF718096),
          size: 20,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildSupportSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).support,
          style: GoogleFonts.instrumentSans(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D3748),
          ),
        ),
        const SizedBox(height: 16),
        _buildSupportItem(
          icon: Icons.star_outline,
          title: AppLocalizations.of(context).leaveAReview,
          onTap: () async {
            await ReviewService.requestReview(context);
          },
        ),
        const SizedBox(height: 12),
        _buildSupportItem(
          icon: Icons.lightbulb_outline,
          title: 'Feature Request',
          onTap: () {
            // Navigate to feature request
          },
        ),
        const SizedBox(height: 30),
        _buildHelpButton(),
      ],
    );
  }

  Widget _buildSupportItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF0D76FF),
          width: 1,
        ),
      ),
      child: ListTile(
        onTap: onTap,
        leading: Icon(
          icon,
          color: const Color(0xFF0D76FF),
          size: 20,
        ),
        title: Text(
          title,
          style: GoogleFonts.instrumentSans(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF0D76FF),
          ),
        ),
        trailing: const Icon(
          Icons.chevron_right,
          color: Color(0xFF0D76FF),
          size: 20,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildHelpButton() {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const HelpSupportScreen(),
          ),
        );
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF0D76FF).withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.help_outline,
              color: Color(0xFF0D76FF),
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                AppLocalizations.of(context).howCanWeHelpYou,
                style: GoogleFonts.instrumentSans(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF0D76FF),
                ),
              ),
            ),
            const Icon(
              Icons.chevron_right,
              color: Color(0xFF0D76FF),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
