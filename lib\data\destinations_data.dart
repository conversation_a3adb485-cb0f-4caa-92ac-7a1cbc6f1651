import '../services/destination_randomization_service.dart';

class DestinationsData {
  // Static list to store liked destinations in memory (deprecated - use LikedDestinationsService)
  static final List<Map<String, dynamic>> _likedDestinations = [];
  static final List<Map<String, dynamic>> _passedDestinations = [];

  /// Get destinations with daily randomization and progress preservation
  static Future<List<Map<String, dynamic>>> getDestinations() async {
    return await DestinationRandomizationService.getDestinationsFromProgress();
  }

  /// Get destinations in original order (for backward compatibility)
  static List<Map<String, dynamic>> getDestinationsOriginalOrder() {
    return [
      {
        'id': '1',
        'name': 'Raja Ampat',
        'location': 'West Papua, Indonesia',
        'image':
            'https://images.unsplash.com/photo-1703769605297-cc74106244d9?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8UmFqYSUyMEFtcGF0fGVufDB8fDB8fHww',
        'rating': 4.9,
        'distance': 2847,
        'description':
            'Known as the "Four Kings", Raja Ampat is a pristine marine paradise with the richest marine biodiversity on Earth.',
        'tags': ['Diving', 'Marine Life', 'Remote', 'Adventure'],
      },
      {
        'id': '2',
        'name': 'Santorini',
        'location': 'Cyclades, Greece',
        'image':
            'https://plus.unsplash.com/premium_photo-1661964149725-fbf14eabd38c?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8U2FudG9yaW5pfGVufDB8fDB8fHww',
        'rating': 4.8,
        'distance': 8234,
        'description':
            'Famous for its stunning sunsets, white-washed buildings, and crystal-clear waters.',
        'tags': ['Sunset', 'Romance', 'Architecture', 'Mediterranean'],
      },
      {
        'id': '3',
        'name': 'Kyoto',
        'location': 'Kansai, Japan',
        'image':
            'https://images.unsplash.com/photo-1526481280693-3bfa7568e0f3?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fEt5b3RvfGVufDB8fDB8fHww',
        'rating': 4.7,
        'distance': 5892,
        'description':
            'Ancient capital with thousands of temples, traditional gardens, and preserved historic districts.',
        'tags': ['Culture', 'Temples', 'History', 'Traditional'],
      },
      {
        'id': '4',
        'name': 'Banff National Park',
        'location': 'Alberta, Canada',
        'image':
            'https://images.unsplash.com/photo-1561134643-668f9057cce4?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8QmFuZmYlMjBOYXRpb25hbCUyMFBhcmt8ZW58MHx8MHx8fDA%3D',
        'rating': 4.9,
        'distance': 12456,
        'description':
            'Stunning mountain landscapes, pristine lakes, and abundant wildlife in the Canadian Rockies.',
        'tags': ['Mountains', 'Nature', 'Hiking', 'Wildlife'],
      },
      {
        'id': '5',
        'name': 'Maldives',
        'location': 'Indian Ocean',
        'image':
            'https://images.unsplash.com/photo-1586500038052-b831efc02314?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NTV8fE1hbGRpdmVzfGVufDB8fDB8fHww',
        'rating': 4.8,
        'distance': 3421,
        'description':
            'Tropical paradise with overwater bungalows, crystal-clear lagoons, and pristine coral reefs.',
        'tags': ['Beach', 'Luxury', 'Diving', 'Romance'],
      },
      {
        'id': '6',
        'name': 'Machu Picchu',
        'location': 'Cusco, Peru',
        'image':
            'https://plus.unsplash.com/premium_photo-1694475501155-2f344cea9eb3?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8TWFjaHUlMjBQaWNjaHV8ZW58MHx8MHx8fDA%3D',
        'rating': 4.9,
        'distance': 18234,
        'description':
            'Ancient Incan citadel perched high in the Andes Mountains, a UNESCO World Heritage Site.',
        'tags': ['History', 'Hiking', 'Ancient', 'Mountains'],
      },
      {
        'id': '7',
        'name': 'Iceland',
        'location': 'Nordic Island',
        'image':
            'https://images.unsplash.com/photo-1529963183134-61a90db47eaf?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTJ8fEljZWxhbmR8ZW58MHx8MHx8fDA%3D',
        'rating': 4.8,
        'distance': 9876,
        'description':
            'Land of fire and ice with dramatic waterfalls, geysers, glaciers, and the Northern Lights.',
        'tags': ['Northern Lights', 'Waterfalls', 'Glaciers', 'Adventure'],
      },
      {
        'id': '8',
        'name': 'Bali',
        'location': 'Indonesia',
        'image':
            'https://plus.unsplash.com/premium_photo-1661878915254-f3163e91d870?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8QmFsaXxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.7,
        'distance': 2156,
        'description':
            'Tropical paradise with ancient temples, lush rice terraces, and vibrant cultural traditions.',
        'tags': ['Beach', 'Culture', 'Temples', 'Rice Terraces'],
      },
      {
        'id': '9',
        'name': 'Swiss Alps',
        'location': 'Switzerland',
        'image':
            'https://images.unsplash.com/photo-1617256955938-598efba0ea5e?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8U3dpc3MlMjBBbHBzfGVufDB8fDB8fHww',
        'rating': 4.9,
        'distance': 8765,
        'description':
            'Majestic mountain peaks, pristine lakes, and charming alpine villages.',
        'tags': ['Mountains', 'Skiing', 'Lakes', 'Alpine'],
      },
      {
        'id': '10',
        'name': 'Serengeti',
        'location': 'Tanzania',
        'image':
            'https://images.unsplash.com/photo-1689174989585-3d8c5382abc1?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjJ8fFNlcmVuZ2V0aXxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.8,
        'distance': 7432,
        'description':
            'Witness the Great Migration and experience incredible wildlife in this iconic African savanna.',
        'tags': ['Safari', 'Wildlife', 'Migration', 'Adventure'],
      },
      {
        'id': '11',
        'name': 'Norwegian Fjords',
        'location': 'Norway',
        'image':
            'https://images.unsplash.com/photo-1664825381616-5cb8397fd9b1?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Tm9yd2VnaWFuJTIwRmpvcmRzfGVufDB8fDB8fHww',
        'rating': 4.9,
        'distance': 9234,
        'description':
            'Dramatic landscapes with deep fjords, towering waterfalls, and pristine wilderness.',
        'tags': ['Fjords', 'Waterfalls', 'Cruise', 'Nature'],
      },
      {
        'id': '12',
        'name': 'Great Barrier Reef',
        'location': 'Queensland, Australia',
        'image':
            'https://images.unsplash.com/photo-1582623838120-455da222cdc7?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8R3JlYXQlMjBCYXJyaWVyJTIwUmVlZnxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.8,
        'distance': 6789,
        'description':
            'World\'s largest coral reef system with incredible marine biodiversity and diving opportunities.',
        'tags': ['Diving', 'Marine Life', 'Coral Reef', 'Snorkeling'],
      },
      {
        'id': '13',
        'name': 'Tokyo',
        'location': 'Kanto, Japan',
        'image':
            'https://plus.unsplash.com/premium_photo-1661914240950-b0124f20a5c1?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8VG9reW98ZW58MHx8MHx8fDA%3D',
        'rating': 4.7,
        'distance': 5432,
        'description':
            'Vibrant metropolis blending ultra-modern technology with traditional culture, incredible food scene.',
        'tags': [
          'Urban',
          'Culture',
          'Food',
          'Technology',
          'Nightlife',
          'Shopping'
        ],
      },
      {
        'id': '14',
        'name': 'Patagonia',
        'location': 'Argentina & Chile',
        'image':
            'https://images.unsplash.com/photo-1558517286-8a9cb0b8c793?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8UGF0YWdvbmlhfGVufDB8fDB8fHww',
        'rating': 4.9,
        'distance': 19876,
        'description':
            'Vast wilderness with dramatic landscapes, glaciers, and some of the world\'s best trekking.',
        'tags': [
          'Adventure',
          'Hiking',
          'Nature',
          'Remote',
          'Glaciers',
          'Wildlife'
        ],
      },
      {
        'id': '15',
        'name': 'Amalfi Coast',
        'location': 'Campania, Italy',
        'image':
            'https://images.unsplash.com/photo-1561956021-947f09ae0101?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8QW1hbGZpJTIwQ29hc3R8ZW58MHx8MHx8fDA%3D',
        'rating': 4.8,
        'distance': 7654,
        'description':
            'Stunning coastal drive with colorful cliffside villages, luxury resorts, and Mediterranean charm.',
        'tags': [
          'Romance',
          'Luxury',
          'Coastal',
          'Mediterranean',
          'Photography',
          'Fine Dining'
        ],
      },
      {
        'id': '16',
        'name': 'Marrakech',
        'location': 'Morocco',
        'image':
            'https://plus.unsplash.com/premium_photo-1697730075333-822144628df6?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8TWFycmFrZWNofGVufDB8fDB8fHww',
        'rating': 4.6,
        'distance': 8765,
        'description':
            'Imperial city with vibrant souks, stunning palaces, and rich cultural heritage in North Africa.',
        'tags': [
          'Culture',
          'Historical',
          'Shopping',
          'Architecture',
          'Local Food',
          'Authentic'
        ],
      },
      {
        'id': '17',
        'name': 'New Zealand South Island',
        'location': 'New Zealand',
        'image':
            'https://images.unsplash.com/photo-1701974780596-ee8c971dd9a9?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8TmV3JTIwWmVhbGFuZCUyMFNvdXRoJTIwSXNsYW5kfGVufDB8fDB8fHww',
        'rating': 4.9,
        'distance': 8901,
        'description':
            'Adventure capital with fjords, mountains, lakes, and endless outdoor activities.',
        'tags': [
          'Adventure',
          'Nature',
          'Hiking',
          'Photography',
          'Outdoor',
          'Scenic'
        ],
      },
      {
        'id': '18',
        'name': 'Tulum',
        'location': 'Quintana Roo, Mexico',
        'image':
            'https://images.unsplash.com/photo-1520483601560-389dff434fdf?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8VHVsdW18ZW58MHx8MHx8fDA%3D',
        'rating': 4.7,
        'distance': 15432,
        'description':
            'Bohemian beach town with ancient Mayan ruins, cenotes, and eco-luxury resorts.',
        'tags': [
          'Beach',
          'Historical',
          'Wellness',
          'Eco-Tourism',
          'Cenotes',
          'Yoga'
        ],
      },
      {
        'id': '19',
        'name': 'Dubai',
        'location': 'United Arab Emirates',
        'image':
            'https://plus.unsplash.com/premium_photo-1697729914552-368899dc4757?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8RHViYWl8ZW58MHx8MHx8fDA%3D',
        'rating': 4.6,
        'distance': 4567,
        'description':
            'Ultra-modern city with luxury shopping, futuristic architecture, and desert adventures.',
        'tags': [
          'Luxury',
          'Shopping',
          'Urban',
          'Architecture',
          'Desert',
          'Modern'
        ],
      },
      {
        'id': '20',
        'name': 'Scottish Highlands',
        'location': 'Scotland, UK',
        'image':
            'https://plus.unsplash.com/premium_photo-1673002094273-80b3fa054637?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8U2NvdHRpc2glMjBIaWdobGFuZHN8ZW58MHx8MHx8fDA%3D',
        'rating': 4.8,
        'distance': 9876,
        'description':
            'Rugged landscapes with ancient castles, lochs, and rich Celtic heritage.',
        'tags': [
          'Nature',
          'Historical',
          'Castles',
          'Hiking',
          'Culture',
          'Remote'
        ],
      },
      {
        'id': '21',
        'name': 'Costa Rica',
        'location': 'Central America',
        'image':
            'https://images.unsplash.com/photo-1620658927695-c33df6fb8130?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8Q29zdGElMjBSaWNhfGVufDB8fDB8fHww',
        'rating': 4.8,
        'distance': 16789,
        'description':
            'Biodiversity hotspot with rainforests, volcanoes, beaches, and incredible wildlife.',
        'tags': [
          'Nature',
          'Wildlife',
          'Adventure',
          'Eco-Tourism',
          'Rainforest',
          'Volcanoes'
        ],
      },
      {
        'id': '22',
        'name': 'Petra',
        'location': 'Jordan',
        'image':
            'https://images.unsplash.com/photo-1579606032821-4e6161c81bd3?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8UGV0cmF8ZW58MHx8MHx8fDA%3D',
        'rating': 4.9,
        'distance': 6543,
        'description':
            'Ancient Nabataean city carved into rose-red cliffs, one of the New Seven Wonders.',
        'tags': [
          'Historical',
          'Ancient',
          'Architecture',
          'Desert',
          'UNESCO',
          'Photography'
        ],
      },
      {
        'id': '23',
        'name': 'Bagan',
        'location': 'Myanmar',
        'image':
            'https://images.unsplash.com/photo-1617171985982-c8453a5ee5eb?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTJ8fEJhZ2FufGVufDB8fDB8fHww',
        'rating': 4.8,
        'distance': 3456,
        'description':
            'Ancient city with over 2,000 Buddhist temples and pagodas scattered across mystical plains.',
        'tags': [
          'Historical',
          'Ancient',
          'Temples',
          'Photography',
          'Culture',
          'Spiritual',
          'UNESCO',
          'Hot Air Balloon'
        ],
      },
      {
        'id': '24',
        'name': 'Faroe Islands',
        'location': 'Denmark',
        'image':
            'https://images.unsplash.com/photo-1726384700567-48629fe64a75?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NjB8fEZhcm9lJTIwSXNsYW5kc3xlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.9,
        'distance': 8901,
        'description':
            'Remote Nordic islands with dramatic cliffs, grass-roof houses, and pristine wilderness.',
        'tags': [
          'Remote',
          'Nature',
          'Hiking',
          'Photography',
          'Nordic',
          'Cliffs',
          'Hidden Gem',
          'Peaceful'
        ],
      },
      {
        'id': '25',
        'name': 'Socotra Island',
        'location': 'Yemen',
        'image':
            'https://images.unsplash.com/photo-1642425149790-6067ff132526?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8U29jb3RyYSUyMElzbGFuZHxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.7,
        'distance': 5432,
        'description':
            'Isolated island with unique flora and fauna, often called the "Galápagos of the Indian Ocean".',
        'tags': [
          'Remote',
          'Nature',
          'Wildlife',
          'Unique',
          'Adventure',
          'Hidden Gem',
          'Endemic Species',
          'Eco-Tourism'
        ],
      },
      {
        'id': '26',
        'name': 'Lofoten Islands',
        'location': 'Norway',
        'image':
            'https://images.unsplash.com/photo-1542047078441-229be3aa1f12?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8TG9mb3RlbiUyMElzbGFuZHN8ZW58MHx8MHx8fDA%3D',
        'rating': 4.9,
        'distance': 9234,
        'description':
            'Arctic archipelago with dramatic peaks rising directly from the sea, fishing villages, and Northern Lights.',
        'tags': [
          'Northern Lights',
          'Mountains',
          'Fishing',
          'Photography',
          'Arctic',
          'Scenic',
          'Adventure',
          'Remote'
        ],
      },
      {
        'id': '27',
        'name': 'Cappadocia',
        'location': 'Turkey',
        'image':
            'https://media.istockphoto.com/id/916257120/photo/hot-air-balloons-flying-over-cappadocia-turkey.webp?a=1&b=1&s=612x612&w=0&k=20&c=iuoqIQAzrsz2XZ_NFrXL-q4y7rxYo32cJ6YO8xLmFL8=',
        'rating': 4.8,
        'distance': 7654,
        'description':
            'Surreal landscape with fairy chimneys, underground cities, and hot air balloon rides.',
        'tags': [
          'Hot Air Balloon',
          'Photography',
          'Historical',
          'Unique',
          'Adventure',
          'Underground Cities',
          'Romance',
          'Scenic'
        ],
      },
      {
        'id': '28',
        'name': 'Salar de Uyuni',
        'location': 'Bolivia',
        'image':
            'https://images.unsplash.com/photo-1604688966134-abedc895299f?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTV8fFNhbGFyJTIwZGUlMjBVeXVuaXxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.9,
        'distance': 18765,
        'description':
            'World\'s largest salt flat creating mirror-like reflections and otherworldly landscapes.',
        'tags': [
          'Photography',
          'Unique',
          'Adventure',
          'Scenic',
          'Remote',
          'Mirror Effect',
          'Stargazing',
          'Nature'
        ],
      },
      {
        'id': '29',
        'name': 'Zhangjiajie',
        'location': 'Hunan, China',
        'image':
            'https://images.unsplash.com/photo-1659157198284-e842e55a93d4?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8WmhhbmdqaWFqaWV8ZW58MHx8MHx8fDA%3D',
        'rating': 4.7,
        'distance': 6789,
        'description':
            'Towering sandstone pillars that inspired Avatar\'s floating mountains, with glass bridges and cable cars.',
        'tags': [
          'Mountains',
          'Photography',
          'Adventure',
          'Unique',
          'Glass Bridge',
          'Cable Car',
          'Nature',
          'Hiking'
        ],
      },
      {
        'id': '30',
        'name': 'Easter Island',
        'location': 'Chile',
        'image':
            'https://images.unsplash.com/photo-1597240890437-6d9c2d4c16aa?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8RWFzdGVyJTIwSXNsYW5kfGVufDB8fDB8fHww',
        'rating': 4.6,
        'distance': 19876,
        'description':
            'Remote Polynesian island famous for its mysterious moai statues and rich cultural heritage.',
        'tags': [
          'Historical',
          'Remote',
          'Culture',
          'Mystery',
          'Polynesian',
          'Statues',
          'Archaeological',
          'Unique'
        ],
      },
      {
        'id': '31',
        'name': 'Hallstatt',
        'location': 'Austria',
        'image':
            'https://plus.unsplash.com/premium_photo-1661900929391-0185d33a7833?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8SGFsbHN0YXR0fGVufDB8fDB8fHww',
        'rating': 4.8,
        'distance': 8234,
        'description':
            'Picturesque lakeside village with Alpine charm, salt mine history, and fairy-tale architecture.',
        'tags': [
          'Alpine',
          'Photography',
          'Romance',
          'Historical',
          'Lakes',
          'Architecture',
          'Peaceful',
          'Small Town'
        ],
      },
      {
        'id': '32',
        'name': 'Meteora',
        'location': 'Greece',
        'image':
            'https://media.istockphoto.com/id/2173517026/photo/view-of-meteora-with-eastern-orthodox-monasteries-greece.webp?a=1&b=1&s=612x612&w=0&k=20&c=4eYJ08AzIUvBjuNKaJFYMXg_LecK7LBu2N6y4TtKgSw=',
        'rating': 4.9,
        'distance': 7432,
        'description':
            'Monasteries perched on towering rock pillars, creating a mystical and spiritual atmosphere.',
        'tags': [
          'Spiritual',
          'Historical',
          'Monasteries',
          'Photography',
          'Unique',
          'Rock Formations',
          'UNESCO',
          'Hiking'
        ],
      },
      {
        'id': '33',
        'name': 'Antelope Canyon',
        'location': 'Arizona, USA',
        'image':
            'https://images.unsplash.com/photo-1505521377774-103a8cc2f735?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8QW50ZWxvcGUlMjBDYW55b258ZW58MHx8MHx8fDA%3D',
        'rating': 4.8,
        'distance': 13456,
        'description':
            'Stunning slot canyon with ethereal light beams and sculpted sandstone walls.',
        'tags': [
          'Photography',
          'Nature',
          'Unique',
          'Desert',
          'Light Beams',
          'Slot Canyon',
          'Adventure',
          'Scenic'
        ],
      },
      {
        'id': '34',
        'name': 'Giethoorn',
        'location': 'Netherlands',
        'image':
            'https://images.unsplash.com/photo-1673027314770-831fdb815808?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8R2lldGhvb3JufGVufDB8fDB8fHww',
        'rating': 4.7,
        'distance': 8765,
        'description':
            'Venice of the North with canals, thatched-roof farmhouses, and peaceful boat rides.',
        'tags': [
          'Canals',
          'Peaceful',
          'Boat Rides',
          'Architecture',
          'Small Town',
          'Photography',
          'Romance',
          'Hidden Gem'
        ],
      },
      {
        'id': '35',
        'name': 'Plitvice Lakes',
        'location': 'Croatia',
        'image':
            'https://plus.unsplash.com/premium_photo-1661963345194-ae50221eb7bf?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OXx8UGxpdHZpY2UlMjBMYWtlc3xlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.9,
        'distance': 7890,
        'description':
            'Cascading lakes connected by waterfalls in a pristine national park setting.',
        'tags': [
          'Waterfalls',
          'Lakes',
          'Nature',
          'Hiking',
          'Photography',
          'UNESCO',
          'National Park',
          'Scenic'
        ],
      },
      {
        'id': '36',
        'name': 'Phi Phi Islands',
        'location': 'Thailand',
        'image':
            'https://images.unsplash.com/photo-1652867769695-f08d770ce88e?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8UGhpJTIwUGhpJTIwSXNsYW5kc3xlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.6,
        'distance': 2345,
        'description':
            'Tropical paradise with limestone cliffs, crystal-clear waters, and vibrant marine life.',
        'tags': [
          'Beach',
          'Diving',
          'Snorkeling',
          'Marine Life',
          'Tropical',
          'Limestone Cliffs',
          'Island Hopping',
          'Photography'
        ],
      },
      {
        'id': '37',
        'name': 'Dolomites',
        'location': 'Italy',
        'image':
            'https://images.unsplash.com/photo-1628087236657-0cc963ad15fd?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8ZG9sb21pdGVzfGVufDB8fDB8fHww',
        'rating': 4.9,
        'distance': 8123,
        'description':
            'Dramatic mountain range with jagged peaks, alpine meadows, and world-class hiking.',
        'tags': [
          'Mountains',
          'Hiking',
          'Alpine',
          'Photography',
          'Nature',
          'Skiing',
          'UNESCO',
          'Adventure'
        ],
      },
      {
        'id': '38',
        'name': 'Angkor Wat',
        'location': 'Cambodia',
        'image':
            'https://plus.unsplash.com/premium_photo-1661924223647-40abbac077c0?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTN8fGFuZ2tvciUyMHdhdHxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.8,
        'distance': 3567,
        'description':
            'Magnificent temple complex and largest religious monument in the world.',
        'tags': [
          'Historical',
          'Temples',
          'Ancient',
          'UNESCO',
          'Culture',
          'Architecture',
          'Spiritual',
          'Photography'
        ],
      },
      {
        'id': '39',
        'name': 'Fernando de Noronha',
        'location': 'Brazil',
        'image':
            'https://media.istockphoto.com/id/2164609456/photo/scenic-view-of-fernando-de-noronha-against-sky.webp?a=1&b=1&s=612x612&w=0&k=20&c=UBERs5h4taV8RQz8wRwGJ-XhG1r8ZZGy6ZNCfaq-pII=',
        'rating': 4.9,
        'distance': 17890,
        'description':
            'Remote volcanic archipelago with pristine beaches, dolphins, and world-class diving.',
        'tags': [
          'Beach',
          'Diving',
          'Marine Life',
          'Remote',
          'Dolphins',
          'Volcanic',
          'Eco-Tourism',
          'Hidden Gem'
        ],
      },
      {
        'id': '40',
        'name': 'Lapland',
        'location': 'Finland',
        'image':
            'https://media.istockphoto.com/id/2154139409/photo/panoramic-aerial-view-of-gammelstad-church-town-seen-in-winter-at-dusk-lulea-sweden.webp?a=1&b=1&s=612x612&w=0&k=20&c=apixWJiEUtR74ViJhT1SsgcAbPM2MoI58ZaTJlJf0E0=',
        'rating': 4.8,
        'distance': 9456,
        'description':
            'Arctic wonderland with Northern Lights, reindeer, and traditional Sami culture.',
        'tags': [
          'Northern Lights',
          'Arctic',
          'Reindeer',
          'Culture',
          'Winter Activities',
          'Sami',
          'Remote',
          'Adventure'
        ],
      },
      {
        'id': '41',
        'name': 'Socotra Island',
        'location': 'Yemen',
        'image':
            'https://media.istockphoto.com/id/1398180416/photo/bottle-tree-on-a-mountain-site-in-socotra-yemen-taken-in-november-2021.webp?a=1&b=1&s=612x612&w=0&k=20&c=r9ZUU5WL0-UMYUZi7B6axOfQKTikuyMKYLtXc8ktZOo=',
        'rating': 4.7,
        'distance': 5432,
        'description':
            'Isolated island with unique flora and fauna, often called the "Galápagos of the Indian Ocean".',
        'tags': [
          'Remote',
          'Nature',
          'Wildlife',
          'Unique',
          'Adventure',
          'Hidden Gem',
          'Endemic Species',
          'Eco-Tourism'
        ],
      },
      {
        'id': '42',
        'name': 'Azores',
        'location': 'Portugal',
        'image':
            'https://images.unsplash.com/photo-1597765057135-57a85bc9ef51?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8YXpvcmVzfGVufDB8fDB8fHww',
        'rating': 4.8,
        'distance': 9123,
        'description':
            'Volcanic islands with hot springs, crater lakes, and lush green landscapes.',
        'tags': [
          'Volcanic',
          'Hot Springs',
          'Lakes',
          'Nature',
          'Hiking',
          'Wellness',
          'Remote',
          'Photography'
        ],
      },
      {
        'id': '43',
        'name': 'Kangaroo Island',
        'location': 'Australia',
        'image':
            'https://images.unsplash.com/photo-1652593707316-246dec33aed8?q=80&w=387&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        'rating': 4.7,
        'distance': 8901,
        'description':
            'Wildlife sanctuary with native animals, pristine beaches, and rugged coastlines.',
        'tags': [
          'Wildlife',
          'Nature',
          'Beach',
          'Kangaroos',
          'Eco-Tourism',
          'Photography',
          'Adventure',
          'Remote'
        ],
      },
      {
        'id': '44',
        'name': 'Vanuatu',
        'location': 'Pacific Ocean',
        'image':
            'https://plus.unsplash.com/premium_photo-1664304458186-9a67c1330d02?q=80&w=390&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        'rating': 4.6,
        'distance': 11234,
        'description':
            'Tropical paradise with active volcanoes, blue holes, and traditional Melanesian culture.',
        'tags': [
          'Tropical',
          'Volcanoes',
          'Diving',
          'Culture',
          'Adventure',
          'Blue Holes',
          'Remote',
          'Authentic'
        ],
      },
      {
        'id': '45',
        'name': 'Bhutan',
        'location': 'Himalayas',
        'image':
            'https://plus.unsplash.com/premium_photo-1697730342301-c55434fbc3c9?q=80&w=387&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        'rating': 4.9,
        'distance': 4567,
        'description':
            'Last Shangri-La with pristine Buddhist culture, monasteries, and Himalayan landscapes.',
        'tags': [
          'Culture',
          'Spiritual',
          'Monasteries',
          'Mountains',
          'Hiking',
          'Buddhism',
          'Authentic',
          'Remote'
        ],
      },
      {
        'id': '46',
        'name': 'Madagascar',
        'location': 'Indian Ocean',
        'image':
            'https://images.unsplash.com/photo-1590426911380-bd4a61d4d3ea?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTV8fE1hZGFnYXNjYXJ8ZW58MHx8MHx8fDA%3D',
        'rating': 4.7,
        'distance': 7890,
        'description':
            'Unique island with lemurs, baobab trees, and diverse ecosystems found nowhere else.',
        'tags': [
          'Wildlife',
          'Lemurs',
          'Baobab Trees',
          'Nature',
          'Unique',
          'Adventure',
          'Photography',
          'Eco-Tourism'
        ],
      },
      {
        'id': '47',
        'name': 'Kamchatka Peninsula',
        'location': 'Russia',
        'image':
            'https://plus.unsplash.com/premium_photo-1709606770641-ae1de38e8c7a?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTN8fEthbWNoYXRrYSUyMFBlbmluc3VsYXxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.8,
        'distance': 12345,
        'description':
            'Remote wilderness with active volcanoes, geysers, and brown bears.',
        'tags': [
          'Remote',
          'Volcanoes',
          'Geysers',
          'Wildlife',
          'Bears',
          'Adventure',
          'Nature',
          'Extreme'
        ],
      },
      {
        'id': '48',
        'name': 'Palawan',
        'location': 'Philippines',
        'image':
            'https://images.unsplash.com/photo-1584640161267-869f0aa03af6?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8UGFsYXdhbnxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.8,
        'distance': 2890,
        'description':
            'Pristine island with underground rivers, limestone cliffs, and crystal-clear lagoons.',
        'tags': [
          'Beach',
          'Underground River',
          'Limestone Cliffs',
          'Diving',
          'Lagoons',
          'Nature',
          'Adventure',
          'Tropical'
        ],
      },
      {
        'id': '49',
        'name': 'Wadi Rum',
        'location': 'Jordan',
        'image':
            'https://images.unsplash.com/photo-1574681088324-6f9f67e0bb44?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8V2FkaSUyMFJ1bXxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.8,
        'distance': 6543,
        'description':
            'Valley of the Moon with red sand dunes, rock bridges, and Bedouin culture.',
        'tags': [
          'Desert',
          'Adventure',
          'Bedouin Culture',
          'Stargazing',
          'Rock Formations',
          'Photography',
          'Camping',
          'Unique'
        ],
      },
      {
        'id': '50',
        'name': 'Seychelles',
        'location': 'Indian Ocean',
        'image':
            'https://images.unsplash.com/photo-1624964651025-5bf433458ca8?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8U2V5Y2hlbGxlc3xlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.9,
        'distance': 6789,
        'description':
            'Pristine tropical islands with granite boulders, white sand beaches, and luxury resorts.',
        'tags': [
          'Beach',
          'Luxury',
          'Tropical',
          'Romance',
          'Granite Boulders',
          'Diving',
          'Photography',
          'Honeymoon'
        ],
      },
      {
        'id': '51',
        'name': 'Galapagos Islands',
        'location': 'Ecuador',
        'image':
            'https://plus.unsplash.com/premium_photo-1664304455335-602b6811149a?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8R2FsYXBhZ29zJTIwSXNsYW5kc3xlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.9,
        'distance': 17234,
        'description':
            'Unique wildlife sanctuary with giant tortoises, marine iguanas, and pristine ecosystems.',
        'tags': [
          'Wildlife',
          'Eco-Tourism',
          'Marine Life',
          'Unique',
          'Tortoises',
          'Iguanas',
          'Nature',
          'Educational'
        ],
      },
      {
        'id': '52',
        'name': 'Faroe Islands',
        'location': 'Denmark',
        'image':
            'https://media.istockphoto.com/id/1699805301/photo/faroe-islands-mulafossur-waterfall-in-summer-gasadalur-v%C3%A1gar-island.webp?a=1&b=1&s=612x612&w=0&k=20&c=m-12JEW6BNUuNhpTEwzjXMBLJBmsnddOAwnJcZpOjLY=',
        'rating': 4.8,
        'distance': 8901,
        'description':
            'Remote Nordic islands with dramatic cliffs, grass-roof houses, and pristine wilderness.',
        'tags': [
          'Remote',
          'Nature',
          'Hiking',
          'Photography',
          'Nordic',
          'Cliffs',
          'Hidden Gem',
          'Peaceful'
        ],
      },
      {
        'id': '53',
        'name': 'Socotra Island',
        'location': 'Yemen',
        'image':
            'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80',
        'rating': 4.7,
        'distance': 5432,
        'description':
            'Isolated island with unique flora and fauna, often called the "Galápagos of the Indian Ocean".',
        'tags': [
          'Remote',
          'Nature',
          'Wildlife',
          'Unique',
          'Adventure',
          'Hidden Gem',
          'Endemic Species',
          'Eco-Tourism'
        ],
      },
      {
        'id': '54',
        'name': 'Lofoten Islands',
        'location': 'Norway',
        'image':
            'https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80',
        'rating': 4.9,
        'distance': 9234,
        'description':
            'Arctic archipelago with dramatic peaks rising directly from the sea, fishing villages, and Northern Lights.',
        'tags': [
          'Northern Lights',
          'Mountains',
          'Fishing',
          'Photography',
          'Arctic',
          'Scenic',
          'Adventure',
          'Remote'
        ],
      },
      {
        'id': '55',
        'name': 'Raja Ampat',
        'location': 'West Papua, Indonesia',
        'image':
            'https://images.unsplash.com/photo-1703769605293-2280634db93b?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fFJhamElMjBBbXBhdHxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.9,
        'distance': 2847,
        'description':
            'Known as the "Four Kings", Raja Ampat is a pristine marine paradise with the richest marine biodiversity on Earth.',
        'tags': [
          'Diving',
          'Marine Life',
          'Remote',
          'Adventure',
          'Coral Reef',
          'Biodiversity',
          'Underwater Photography',
          'Pristine'
        ],
      },
      {
        'id': '56',
        'name': 'Socotra Island',
        'location': 'Yemen',
        'image':
            'https://images.unsplash.com/photo-1642425149981-326417d9ae82?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8U29jb3RyYSUyMElzbGFuZHxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.7,
        'distance': 5432,
        'description':
            'Isolated island with unique flora and fauna, often called the "Galápagos of the Indian Ocean".',
        'tags': [
          'Remote',
          'Nature',
          'Wildlife',
          'Unique',
          'Adventure',
          'Hidden Gem',
          'Endemic Species',
          'Eco-Tourism'
        ],
      },
      {
        'id': '57',
        'name': 'Ladakh',
        'location': 'India',
        'image':
            'https://images.unsplash.com/photo-1581793745862-99fde7fa73d2?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8TGFkYWtofGVufDB8fDB8fHww',
        'rating': 4.8,
        'distance': 4321,
        'description':
            'High-altitude desert with Buddhist monasteries, pristine lakes, and dramatic mountain landscapes.',
        'tags': [
          'Mountains',
          'Buddhism',
          'Monasteries',
          'High Altitude',
          'Adventure',
          'Photography',
          'Spiritual',
          'Remote'
        ],
      },
      {
        'id': '58',
        'name': 'Svalbard',
        'location': 'Norway',
        'image':
            'https://plus.unsplash.com/premium_photo-1674331205709-d34cfe333dfb?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8U3ZhbGJhcmR8ZW58MHx8MHx8fDA%3D',
        'rating': 4.7,
        'distance': 11234,
        'description':
            'Arctic wilderness with polar bears, glaciers, and the midnight sun.',
        'tags': [
          'Arctic',
          'Polar Bears',
          'Glaciers',
          'Midnight Sun',
          'Adventure',
          'Wildlife',
          'Remote',
          'Extreme'
        ],
      },
      {
        'id': '59',
        'name': 'Saguenay Fjord',
        'location': 'Quebec, Canada',
        'image':
            'https://images.unsplash.com/photo-1612500434985-23f2674e6c2c?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8U2FndWVuYXklMjBGam9yZHxlbnwwfHwwfHx8MA%3D%3D',
        'rating': 4.7,
        'distance': 12890,
        'description':
            'One of the world\'s southernmost fjords with beluga whales and dramatic landscapes.',
        'tags': [
          'Fjords',
          'Whales',
          'Nature',
          'Wildlife',
          'Photography',
          'Cruise',
          'Remote',
          'Scenic'
        ],
      },
      {
        'id': '60',
        'name': 'Tasmania',
        'location': 'Australia',
        'image':
            'https://images.unsplash.com/photo-1595215296432-4abf071b4d91?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8VGFzbWFuaWF8ZW58MHx8MHx8fDA%3D',
        'rating': 4.8,
        'distance': 9876,
        'description':
            'Island state with pristine wilderness, unique wildlife, and world-class hiking trails.',
        'tags': [
          'Wildlife',
          'Hiking',
          'Nature',
          'Wilderness',
          'Photography',
          'Adventure',
          'Tasmanian Devil',
          'Remote'
        ],
      },
    ];
  }

  static List<Map<String, dynamic>> getLikedDestinations() {
    return List.from(_likedDestinations);
  }

  static void addLikedDestination(Map<String, dynamic> destination) {
    // Check if destination is not already liked
    if (!_likedDestinations.any((d) => d['id'] == destination['id'])) {
      _likedDestinations.add(destination);
      print('Liked destination: ${destination['name']}');
    }
  }

  static void removeLikedDestination(Map<String, dynamic> destination) {
    _likedDestinations.removeWhere((d) => d['id'] == destination['id']);
    print('Removed liked destination: ${destination['name']}');
  }

  static bool isDestinationLiked(Map<String, dynamic> destination) {
    return _likedDestinations.any((d) => d['id'] == destination['id']);
  }

  static void addPassedDestination(Map<String, dynamic> destination) {
    // Check if destination is not already passed
    if (!_passedDestinations.any((d) => d['id'] == destination['id'])) {
      _passedDestinations.add(destination);
      print('Passed on destination: ${destination['name']}');
    }
  }

  static List<Map<String, dynamic>> getPassedDestinations() {
    return List.from(_passedDestinations);
  }
}
