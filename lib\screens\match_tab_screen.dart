import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/match_service.dart';
import '../services/swipe_tracking_service.dart';
import '../services/subscription_service.dart';
import '../services/admob_service.dart';
import '../services/ad_frequency_service.dart';
import '../services/liked_destinations_service.dart';
import '../services/auth_service.dart';
import '../services/destination_randomization_service.dart';
import '../widgets/flutter_card_swiper_widget.dart';
import '../widgets/swipe_limit_dialog.dart';
import '../widgets/optimized_network_image.dart';
import '../widgets/add_to_itinerary_dialog.dart';
import '../data/destinations_data.dart';
import '../generated/l10n/app_localizations.dart';
import 'match_screen.dart';

class MatchTabScreen extends StatefulWidget {
  const MatchTabScreen({super.key});

  @override
  State<MatchTabScreen> createState() => _MatchTabScreenState();
}

class _MatchTabScreenState extends State<MatchTabScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  List<Map<String, dynamic>> _destinations = [];
  bool _isLoadingDestinations = true;
  List<Map<String, dynamic>> _likedDestinations = [];
  bool _isLoadingLikedDestinations = true;

  // Card swiper state preservation
  final GlobalKey _cardSwiperKey = GlobalKey();
  int _currentCardIndex = 0;
  int _totalSwipeProgress = 0; // Track total progress in the full list
  bool _destinationsLoaded = false; // Track if destinations have been loaded

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Check if it's a new day and reset state if needed
    _checkForNewDay();

    // Initialize fade animation for tab transitions
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Listen to tab changes for animations
    _tabController.addListener(_handleTabChange);

    // Load destinations and initialize services
    _loadDestinations();
    _initializeAdServices();
  }

  Future<void> _checkForNewDay() async {
    try {
      final lastRandomizationDate =
          await DestinationRandomizationService.getLastRandomizationDate();
      final now = DateTime.now();

      if (lastRandomizationDate != null) {
        final isNewDay = lastRandomizationDate.year != now.year ||
            lastRandomizationDate.month != now.month ||
            lastRandomizationDate.day != now.day;

        if (isNewDay) {
          if (kDebugMode) {
            print(
                'MatchTabScreen: New day detected, resetting destinations state');
          }
          setState(() {
            _destinationsLoaded = false;
            _totalSwipeProgress = 0;
            _currentCardIndex = 0;
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('MatchTabScreen: Error checking for new day: $e');
      }
    }
  }

  Future<void> _loadDestinations() async {
    // Only load destinations once per session unless it's a new day
    if (_destinationsLoaded) {
      if (kDebugMode) {
        print('MatchTabScreen: Destinations already loaded, skipping reload');
      }
      return;
    }

    try {
      // Load full destination list with progress information
      final result = await DestinationsData.getDestinationsWithProgress();
      final allDestinations = result.destinations;
      final swipeProgress = result.progressIndex;

      setState(() {
        _destinations = allDestinations;
        _totalSwipeProgress = swipeProgress;
        _currentCardIndex = swipeProgress; // Start from the progress point
        _isLoadingDestinations = false;
        _destinationsLoaded = true;
      });

      if (kDebugMode) {
        print(
            'MatchTabScreen: Loaded ${allDestinations.length} destinations, starting from index: $swipeProgress');
      }

      _fadeController.forward();
    } catch (e) {
      if (kDebugMode) {
        print('MatchTabScreen: Error loading destinations: $e');
      }
      setState(() {
        _destinations = DestinationsData.getDestinationsOriginalOrder();
        _totalSwipeProgress = 0;
        _currentCardIndex = 0;
        _isLoadingDestinations = false;
        _destinationsLoaded = true;
      });
      _fadeController.forward();
    }
  }

  Future<void> _loadLikedDestinations() async {
    try {
      final userId = AuthService.currentUser?.id;
      final likedDestinations =
          await LikedDestinationsService.getLikedDestinations(userId: userId);
      setState(() {
        _likedDestinations = likedDestinations;
        _isLoadingLikedDestinations = false;
      });
    } catch (e) {
      setState(() {
        _likedDestinations = DestinationsData.getLikedDestinations();
        _isLoadingLikedDestinations = false;
      });
    }
  }

  /// Initialize AdMob and ad frequency services
  Future<void> _initializeAdServices() async {
    try {
      // Temporarily disable AdMob initialization until platform setup is complete
      // await AdMobService.initialize();
      await AdFrequencyService.initialize();
      debugPrint(
          'Ad services initialization skipped - platform setup required');
    } catch (e) {
      if (mounted) {
        // Handle initialization error silently in production
        debugPrint('Ad services initialization error: $e');
      }
    }
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      // Load liked destinations when switching to liked tab
      if (_tabController.index == 1) {
        _loadLikedDestinations();
      }
      // Note: We don't reload destinations for "For You" tab to preserve state

      // Animate out current content
      _fadeController.reverse().then((_) {
        // Animate in new content
        _fadeController.forward();
      });
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F9FC),
      body: SafeArea(
        child: Column(
          children: [
            // Custom TabBar
            _buildCustomTabBar(),

            // Tab Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildForYouTab(),
                    _buildLikedTab(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 70),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(100),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(),
        indicator: BoxDecoration(
          color: const Color(0xFF0D76FF),
          borderRadius: BorderRadius.circular(100),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: const Color(0xFF718096),
        labelStyle: GoogleFonts.instrumentSans(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.instrumentSans(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        tabs: [
          Tab(
            text: AppLocalizations.of(context).forYou,
            height: 40,
          ),
          Tab(
            text: AppLocalizations.of(context).liked,
            height: 40,
          ),
        ],
      ),
    );
  }

  Widget _buildForYouTab() {
    if (_isLoadingDestinations) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: FlutterCardSwiperWidget(
        key: _cardSwiperKey,
        destinations: _destinations,
        initialIndex: _currentCardIndex,
        onIndexChanged: (index) {
          setState(() {
            _currentCardIndex = _totalSwipeProgress + index;
          });
        },
        onAddToItinerary: _showAddToItineraryDialog,
        onCardSwiped: (destination, isLike) async {
          // Check swipe limits before processing the swipe
          final canSwipe = await SwipeTrackingService.canSwipe();
          if (!canSwipe) {
            // Show swipe limit dialog
            await _showSwipeLimitDialog();
            return;
          }

          // Record the swipe
          final swipeRecorded = await SwipeTrackingService.recordSwipe();
          if (!swipeRecorded) {
            // Show swipe limit dialog if recording failed
            await _showSwipeLimitDialog();
            return;
          }

          // Save swipe progress using the absolute position
          final newProgress = _currentCardIndex + 1;
          await DestinationRandomizationService.saveSwipeProgress(newProgress);

          // Update local progress tracking
          setState(() {
            _totalSwipeProgress = newProgress;
          });

          if (kDebugMode) {
            print(
                'MatchTabScreen: Swiped ${destination['name']}, new progress: $newProgress');
          }

          // Check if it's time to show an ad (every 7 swipes for free users)
          final shouldShowAd =
              await AdFrequencyService.recordSwipeAndCheckAdTrigger();
          if (shouldShowAd) {
            await _showInterstitialAd();
          }

          if (isLike) {
            // Save to database
            final userId = AuthService.currentUser?.id;
            await LikedDestinationsService.addLikedDestination(destination,
                userId: userId);

            // Also add to memory for backward compatibility
            DestinationsData.addLikedDestination(destination);

            // Check for match before showing regular feedback
            final matchResult =
                await MatchService.checkDestinationMatch(destination);

            if (matchResult.isMatch) {
              // Show match screen
              _showMatchScreen(destination, matchResult.matchedCategories);
            } else {
              // Show regular feedback
              _showLikedDestinationFeedback(destination);
            }
          } else {
            DestinationsData.addPassedDestination(destination);
          }
        },
      ),
    );
  }

  Widget _buildLikedTab() {
    if (_isLoadingLikedDestinations) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0D76FF)),
        ),
      );
    }

    if (_likedDestinations.isEmpty) {
      return _buildEmptyLikedState();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        physics: const BouncingScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.75,
        ),
        itemCount: _likedDestinations.length,
        itemBuilder: (context, index) {
          final destination = _likedDestinations[index];
          return _buildLikedDestinationCard(destination);
        },
      ),
    );
  }

  Widget _buildEmptyLikedState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFF0D76FF).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.favorite_outline,
              size: 40,
              color: Color(0xFF0D76FF),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Liked Destinations Yet',
            style: GoogleFonts.instrumentSans(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start swiping in the "For You" tab to\ndiscover and like amazing destinations!',
            style: GoogleFonts.instrumentSans(
              fontSize: 14,
              color: const Color(0xFF718096),
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLikedDestinationCard(Map<String, dynamic> destination) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image
          Expanded(
            flex: 3,
            child: ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(16)),
              child: OptimizedNetworkImage(
                imageUrl: destination['image'] ?? '',
                width: double.infinity,
                fit: BoxFit.cover,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
                enableFadeIn: true,
                fadeInDuration: const Duration(milliseconds: 300),
                cacheWidth: 300,
                cacheHeight: 200,
                errorWidget: Container(
                  color: const Color(0xFF0D76FF).withOpacity(0.1),
                  child: const Icon(
                    Icons.image,
                    color: Color(0xFF0D76FF),
                    size: 32,
                  ),
                ),
              ),
            ),
          ),

          // Content
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    destination['name'] ?? 'Unknown',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2D3748),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    destination['location'] ?? '',
                    style: GoogleFonts.instrumentSans(
                      fontSize: 12,
                      color: const Color(0xFF718096),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 14,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${destination['rating'] ?? 0.0}',
                        style: GoogleFonts.instrumentSans(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF2D3748),
                        ),
                      ),
                      const Spacer(),
                      const Icon(
                        Icons.favorite,
                        size: 16,
                        color: Colors.red,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddToItineraryDialog(Map<String, dynamic> destination) {
    if (kDebugMode) {
      print(
          'MatchTabScreen: Showing add to itinerary dialog for destination: ${destination['name']}');
      print('MatchTabScreen: Full destination data: $destination');
    }

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AddToItineraryDialog(
        destination: destination,
        onSuccess: () {
          if (kDebugMode) {
            print(
                'MatchTabScreen: Add to itinerary success callback triggered');
          }
          // Show success feedback
          _showLikedDestinationFeedback(destination);
        },
      ),
    );
  }

  void _showMatchScreen(
      Map<String, dynamic> destination, List<String> matchedCategories) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => MatchScreen(
          destination: destination,
          matchedCategories: matchedCategories,
          onAddToItinerary: () {
            Navigator.of(context).pop();
            _showAddToItineraryDialog(destination);
          },
          onKeepSwiping: () {
            Navigator.of(context).pop();
          },
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  Future<void> _showSwipeLimitDialog() async {
    final swipeStats = await SwipeTrackingService.getSwipeStats();
    final isSubscribed = await SubscriptionService.hasActiveSubscription();

    if (!mounted) return;

    await showSwipeLimitDialog(
      context,
      isSubscribed: isSubscribed,
      currentCount: swipeStats['current_count'] ?? 0,
      dailyLimit: swipeStats['daily_limit'] ?? 20,
      onUpgrade: () {
        // Handle upgrade action - will navigate to paywall
      },
      onDismiss: () {
        // Handle dismiss action
      },
    );
  }

  /// Show interstitial ad and handle the result
  Future<void> _showInterstitialAd() async {
    try {
      final adShown = await AdMobService.showInterstitialAd();
      if (adShown) {
        // Reset the swipe count after successfully showing an ad
        await AdFrequencyService.resetSwipeCount();

        if (mounted) {
          debugPrint('Interstitial ad shown successfully');
        }
      } else {
        if (mounted) {
          debugPrint('Failed to show interstitial ad or user is subscribed');
        }
      }
    } catch (e) {
      if (mounted) {
        debugPrint('Error showing interstitial ad: $e');
      }
    }
  }

  void _showLikedDestinationFeedback(Map<String, dynamic> destination) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.favorite, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'Added ${destination['name']} to your favorites!',
                style: GoogleFonts.instrumentSans(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
